# PromptYoSelf v3

> **Agent-first self-prompting in one Python process**

PromptYoSelf is software that enables Letta agents (github.comletta-ai/letta) to schedule one-off or recurring webhook triggered POST calls to the Letta API with messages to prompt themselves at a later date. These "self-prompts" facilitate learning, reflection, and self-management without external users.

This single-file README combines both the **Project Brief** and detailed **Getting Started** instructions for the latest, minimal Flask + APScheduler + STDIO architecture. It covers vision, guiding principles, technical design, components, schema, MVP criteria, timeline, testing strategy, project layout, quick start, security defaults, and licensing.

---

## 1 Principles

**Principles**:

1. **Agent‑first**: Core prompt delivery to agents is via STDIO JSON‑RPC. Agents schedule new self-prompts with PromptyoS<PERSON> by calling a dedicated, minimal internal HTTP API endpoint (secured by a shared secret/static token, typically on localhost within the deployment environment).
2. **K‑I‑S‑S**: One language (Python), one process (Flask), one DB (SQLite).
3. **Self‑contained**: Zero cloud dependencies or orchestration.
4. **Reliability > fanciness**: Self-prompts must fire on time, be flexible in terms of start date and time and recurrence frequency and number of events.
5. **Vertical slices**: Build UI, API, and DB together each sprint.

---

## 2 · Technical Architecture

| Layer                | Choice                               | Rationale                          |
| -------------------- | ------------------------------------ | ---------------------------------- |
| **Web server & UI**  | Flask 3 + Jinja2                     | Familiar, low overhead             |
| **Scheduling**       | Flask‑APScheduler (APScheduler 4.x)  | In‑process jobs; no external queue |
| **Data**             | SQLite (Postgres optional later)     | Zero‑config, ACID                  |
| **ORM**              | SQLAlchemy 2 via Flask‑SQLAlchemy    | Structured models, migrations      |
| **Agent I/O**        | STDIO JSON‑RPC (newline‑delimited)   | Single pipe per agent process      |
| **Optional Webhook** | HTTP POST with `requests + tenacity` | Outbound only, retried             |
| **Styling**          | Tailwind CDN (no JS build)           | Instant utility classes, dark mode |

### High‑Level Diagram

```text
┌────────────┐   (HTML + POST)   ┌───────────────┐
│  Browser   │◄────────────────►│   Flask app   │─── SQLAlchemy ── sqlite.db
└────────────┘                   │ + APScheduler │
                                 │ + CLI helper  │
                                 └───────┬───────┘
                                         │ STDIO JSON
                                         ▼
                                 ┌───────────────┐
                                 │ Letta Agent   │
                                 └───────────────┘
```

---

## 3 · Key Components

1. **Application Factory**

   * Registers UI blueprints.
   * Registers an optional `/api/*` for general external JSON routes.
   * Registers a required minimal internal API (e.g., `/api/internal/reminders`) for agents to schedule reminders, secured by a shared secret/static token.
   * Initializes Flask‑APScheduler.
2. **APScheduler Job** – `check_due` interval (60 s)

   * Queries `reminders.next_run <= now()`.
   * Pushes JSON‑RPC `reminder.fire` to agent via STDIO.
   * Marks status to **sent** and updates `next_run`.
3. **Process Registry**

   * Ensures one `subprocess.Popen` per agent binary, restarts on exit.
4. **WebhookSender (optional)**

   * Uses `requests` + `tenacity` for outbound POST retries.
5. **Static HTML UI**

   * Plain Jinja2 templates for Projects → Tasks → Reminders.
   * Full‑page `<form>` submissions (CSRF via Flask‑WTF).

---

## 4 · Database Schema Highlights

We retain the v2 schema (`projects`, `tasks`, `self_prompts`, `webhook_deliveries`) with one addition:

```sql
ALTER TABLE self_prompts ADD COLUMN process_name TEXT NOT NULL;
```

This field names the local agent executable to invoke for each self-prompt.

---

## 5 · MVP Criteria

1. **CRUD** Projects, Tasks, Self-Prompts via UI *and* optional `/api/*`.
2. **STDIO Push**: Agent receives JSON, returns ACK, scheduler marks **sent**.
3. **Local dev**: `make dev` or VS Code *Reopen in Container* spins up environment.
4. **Tests**: pytest coverage ≥ 80 %; E2E verifies agent flow.

---

## 6 · Phased Timeline

| Week | Vertical Slice                        |
| ---- | ------------------------------------- |
| 0    | Scaffold repo, CI, DevContainer setup |
| 1    | DB models, Alembic migration #1       |
| 2    | API blueprints (unauthenticated)      |
| 3    | APScheduler + STDIO delivery          |
| 4    | Static HTML UI pages                  |
| 5    | Rate‑limit, CSRF, logging, metrics    |
| 6    | Playwright E2E + Locust load tests    |
| 7    | Docker image, run‑book, soft‑launch   |

---

## 7 · Testing Strategy

* **Unit tests** for models, utilities.
* **Integration tests**: API ↔ DB, Scheduler ↔ ProcessRegistry.
* **E2E tests**: full self-prompt cycle (UI/STDIO).
* **Load tests**: simulate 50 agents, 500 self-prompts/hour.

---

## 8 · Project Layout

```text
./
├── app/            # Flask blueprints & templates
│   ├── api/        # JSON routes (optional)
│   ├── ui/         # Jinja templates & forms
│   └── jobs/       # APScheduler job modules
├── agents/         # Sample agent scripts (echo_agent.py)
├── instance/       # SQLite file
├── tests/          # pytest suites
├── setup.sh        # Dev Container & local bootstrap script
├── requirements.txt
├── requirements-dev.txt
└── .devcontainer/  # VS Code Dev Container config
```

---

## 9 · Quick Start

```bash
# Clone & start in Dev Container (VS Code):
# or run locally via Docker Compose: make dev
make dev

# Open in browser:
http://localhost:5000
```

*Without Docker:*

```bash
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt -r requirements-dev.txt
flask --app app:create_app run --reload
```

---

## 10 · Playwright E2E Tests

Playwright drives the browser-based end-to-end tests located under
`promptyoself/tests_e2e/`. Install the Python package and browser
binaries once:

```bash
pip install -r promptyoself/requirements-dev.txt
playwright install
```

Run the tests locally with:

```bash
PYTHONPATH=promptyoself pytest promptyoself/tests_e2e/ -m playwright
```

In CI environments remember to run `playwright install` so the Chromium,
Firefox and WebKit browsers are available. Tests run headless by default;
on Linux containers ensure the `xvfb` package is installed.

---

## 11 · Security Defaults

* **CSRF**: all HTML forms protected by Flask‑WTF.
* **Rate limits**: 200 requests/hour on `/api/*` via Flask‑Limiter.
* **HTTPS headers**: enforced by Flask‑Talisman in production.

---

## 12 · License

Creative Commons Attribution 4.0 International (CC BY 4.0)
