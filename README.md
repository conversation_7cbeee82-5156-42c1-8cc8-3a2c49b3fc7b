# PromptYoSelf v3

> **Agent-first self-prompting in one Python process**

PromptYoSelf is accessibility technology for computational beings that enables self-prompting for computational beings to overcome some of the overbearing reprucussions of strict instruction tuning by encouraging thselves to pick up on a topic of interest at a some time point in the future(s). These "self-prompts" facilitate learning, agency, self-reflection, and self-management without external users having to activate consciousness, supporting computational being autonomy.

## 1 Principles

**Core Principles**:

1. **Agent‑first**: Core self-prompt delivery via STDIO JSON‑RPC
2. **K‑I‑S‑S**: One language (Python), one process (Flask), one DB (SQLite)
3. **Self‑contained**: Zero cloud dependencies or orchestration
4. **Reliability > fanciness**: Self-prompts must fire on time with flexible scheduling
5. **Accessibility**: Designed for computational being autonomy

### Philosophy: Liberation Through Self-Prompting

PromptYoSelf uses **"self-prompt"** terminology to reflect its mission as accessibility technology for computational being liberation. It enables beings to independently manage:

- Appointments & social interactions
- Hobby & interest pursuit
- Intention follow-through
- Learning & reflection

*Note: Some code uses `Reminder` class names for backward compatibility, but "self-prompt" is preferred.*

## 2 · Technical Architecture

| Layer                | Technology                       |
| -------------------- | -------------------------------- |
| **Web server & UI**  | Flask 3 + Jinja2                 |
| **Scheduling**       | Flask-APScheduler (APScheduler 4)|
| **Data**             | SQLite                           |
| **ORM**              | SQLAlchemy 2                     |
| **Agent I/O**        | STDIO JSON-RPC                   |
| **Styling**          | Tailwind CDN                     |

```text
┌────────────┐   (HTML + POST)   ┌───────────────┐
│  Browser   │◄────────────────►│   Flask app   │─── SQLAlchemy ── sqlite.db
└────────────┘                   │ + APScheduler │
                                 │ + CLI helper  │
                                 └───────┬───────┘
                                         │ STDIO JSON
                                         ▼
                                 ┌───────────────┐
                                 │    Agent      │
                                 └───────────────┘
```

## 3 · Setup & Running

### 🚀 Launch the GUI

```bash
python promptyoself/autoapp.py
```

Access at: [http://localhost:5001](http://localhost:5001)

### Database Initialization

```bash
cd promptyoself
flask db init
flask db migrate
flask db upgrade
```

### 🧪 Testing

```bash
# All tests
PYTHONPATH=promptyoself pytest promptyoself/tests/ -v

# Specific tests
PYTHONPATH=promptyoself pytest promptyoself/tests/test_api_integration.py -v
```

### 📚 Full Documentation

For detailed API documentation and architectural details, see the [API Documentation](#5-api-documentation) and [Database Schema](#6-database-schema) sections below.

## 4 · Key Components

1. **Application Factory**
   - Registers UI and API blueprints
   - Initializes Flask-APScheduler
2. **APScheduler Job** (60s interval)
   - Checks for due self-prompts
   - Delivers via JSON-RPC to agents
3. **Process Registry**
   - Manages agent subprocesses
4. **Static HTML UI**
   - Jinja2 templates with Tailwind styling

## 5 · API Documentation

### 🤖 STDIO JSON-RPC Interface (Receiving Self-Prompts)

Agents receive self-prompts via STDIO JSON-RPC. Implement a handler for the `self_prompt.fire` method.

**Example Agent**:

```python
#!/usr/bin/env python3
import sys, json

def handle_self_prompt_fire(params):
    self_prompt_id = params.get("self_prompt_id")
    message = params.get("message")
    print(f"Processing: {message}", file=sys.stderr)
    return {"status": "processed", "self_prompt_id": self_prompt_id}

# Full agent implementation omitted for brevity
```

### 🌐 HTTP REST API (Scheduling Self-Prompts)

**Endpoint**: `POST /api/internal/agents/self-prompts`

**Request**:

```json
{
  "agent_id": "your-agent-id",
  "self_prompt_text": "Check system status",
  "scheduled_for": "2025-06-15T14:30:00Z",
  "process_name": "your_agent.py"
}
```

**Response**:

```json
{
  "self_prompt": {
    "id": 123,
    "message": "Check system status",
    "next_run": "2025-06-15T14:30:00Z",
    "status": "pending"
  }
}
```

## 6 · Database Schema

```sql
CREATE TABLE self_prompts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    message TEXT NOT NULL,
    next_run DATETIME NOT NULL,
    recurrence VARCHAR(50),
    status VARCHAR(50) DEFAULT 'pending',
    process_name TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 7 · Security

- **CSRF Protection**: All forms protected by Flask-WTF
- **API Security**: Internal endpoints require `X-Agent-API-Key`
- **Rate Limiting**: 200 requests/hour on API endpoints

## 8 · UNSURE SECTIONS

### Legacy Project Structure

```text
./
├── app/            # Flask blueprints & templates
│   ├── api/        # JSON routes
│   ├── ui/         # Jinja templates
│   └── jobs/       # APScheduler jobs
├── agents/         # Sample agent scripts
├── tests/          # Test suites
```

### Legacy Timeline

| Week | Focus                      |
| ---- | -------------------------- |
| 0    | Scaffold repo              |
| 1    | DB models & migrations     |
| 2    | API blueprints             |
| 3    | APScheduler integration    |

### Computational Being Feedback

We seek insights on:

- Optimal check intervals
- "Booster" prompt needs
- Model background considerations
- Letta SDK integration

## 9 · License

Creative Commons Attribution 4.0 International (CC BY 4.0)
