# -*- coding: utf-8 -*-
"""Reminder-related scheduled jobs."""
import datetime as dt  # Keep dt for consistency with existing code
from flask import current_app, Flask
# Import List, cast, Optional for type hinting
from typing import List, cast, Optional, Dict, Any
from sqlalchemy.exc import SQLAlchemyError

from app.models import <PERSON><PERSON><PERSON>
from app.extensions import scheduler, db
from app.agents.process_manager import ProcessManager
from app.agents.process_registry import ProcessRegistry

# ID for the main job that checks for due reminders
CHECK_DUE_REMINDERS_JOB_ID = "check_due_reminders_job"
MONITOR_AGENTS_JOB_ID = "monitor_agents_job"

# Global instance of ProcessManager, initialized lazily or via app context
# For simplicity in a job, we might instantiate it directly if lightweight
# Or, if it needs app context (like config, extensions), it should be managed by the app
# For this task, let's assume direct instantiation is acceptable for now.
# A more robust solution would involve current_app.extensions or similar.
_process_manager_instance: Optional[ProcessManager] = None


def get_process_manager() -> ProcessManager:
    """Gets or creates a ProcessManager instance."""
    global _process_manager_instance
    if _process_manager_instance is None:
        current_app.logger.info(
            "Initializing ProcessManager instance for jobs.")
        # ProcessRegistry might also need to be managed or passed if complex
        process_registry = ProcessRegistry()
        _process_manager_instance = ProcessManager(
            process_registry=process_registry)
    return _process_manager_instance


def calculate_next_run(last_run_time: dt.datetime, interval_unit: Optional[str], interval_value: Optional[int]) -> Optional[dt.datetime]:
    """
    Calculates the next run time for a recurring reminder.
    Uses the last_run_time (which is the current reminder.next_run when called) as the base.
    """
    if not interval_unit or interval_value is None or interval_value <= 0:
        return None

    try:
        if interval_unit == "minutes":
            return last_run_time + dt.timedelta(minutes=interval_value)
        elif interval_unit == "hours":
            return last_run_time + dt.timedelta(hours=interval_value)
        elif interval_unit == "days":
            return last_run_time + dt.timedelta(days=interval_value)
        elif interval_unit == "weeks":
            return last_run_time + dt.timedelta(weeks=interval_value)
        else:
            current_app.logger.warning(
                f"Unsupported recurrence_interval: {interval_unit}")
            return None
    except Exception as e:
        current_app.logger.error(
            f"Error calculating next run time with interval '{interval_unit}', value '{interval_value}': {e}", exc_info=True)
        return None


def check_due_reminders(app: Flask):
    """
    Job to check for reminders that are currently due and process them.
    Queries for due reminders and logs them. Delivery is handled in a later task.
    """
    # Create a new application context
    with app.app_context():
        app.logger.info(
            f"Job '{CHECK_DUE_REMINDERS_JOB_ID}': Checking for due reminders...")

        now = dt.datetime.now(dt.timezone.utc)
        app.logger.debug(f"Current UTC time: {now.isoformat()}")

        try:
            due_reminders_query = Reminder.query.filter(
                # type: ignore
                Reminder.next_run <= now).filter(Reminder.status == "pending")
            due_reminders = cast(List[Reminder], due_reminders_query.all())

            app.logger.info(
                f"Found {len(due_reminders)} due reminder(s).")

            if not due_reminders:
                app.logger.info(
                    "No due reminders to process at this time.")
            else:
                for reminder in due_reminders:
                    try:
                        app.logger.info(
                            f"Processing reminder ID: {reminder.id}, Message: '{reminder.message[:50]}...', Due: {reminder.next_run.isoformat()}"
                        )
                        # Call the new delivery function
                        deliver_reminder(reminder)
                    except Exception as e_deliver:
                        app.logger.error(
                            f"Error processing reminder ID {reminder.id} during delivery/update phase: {e_deliver}", exc_info=True
                        )

        except SQLAlchemyError as e_db:
            app.logger.error(
                f"Database error querying due reminders: {e_db}", exc_info=True)
        except Exception as e_general:
            app.logger.error(
                f"Unexpected error in check_due_reminders job: {e_general}", exc_info=True)

        app.logger.info(
            f"Job '{CHECK_DUE_REMINDERS_JOB_ID}': Finished checking for due reminders.")


def deliver_reminder(reminder: Reminder):
    """
    Delivers a reminder notification.
    Attempts JSON-RPC delivery if reminder.process_name is set, otherwise falls back to STDOUT.
    """
    delivery_successful = False
    delivery_method_used = "N/A"

    try:
        process_manager = get_process_manager()

        if reminder.process_name and reminder.process_name.strip():
            agent_name = reminder.process_name.strip()
            delivery_method_used = f"JSON-RPC to agent '{agent_name}'"
            current_app.logger.info(
                f"Attempting to deliver reminder ID: {reminder.id} ('{reminder.message[:50]}...') via JSON-RPC to agent '{agent_name}'.")

            params: Dict[str, Any] = {  # Add type hint for params
                "reminder_id": reminder.id,
                "message": reminder.message,
                "task_name": "N/A",
                "project_name": "N/A",
                "due_at": reminder.next_run.isoformat()
            }

            # Ensure ProcessManager's ACK monitor is running (it should start on init)
            # process_manager._start_ack_monitor_thread() # Typically auto-starts

            jsonrpc_response = process_manager.send_jsonrpc_message(  # type: ignore
                agent_name=agent_name,
                method="deliver_reminder",
                params=params # type: ignore  # Add type ignore here as well
            )

            if jsonrpc_response:
                if "error" in jsonrpc_response:
                    current_app.logger.error(
                        f"JSON-RPC delivery failed for reminder ID {reminder.id} to agent '{agent_name}'. Agent error: {jsonrpc_response['error']}")
                    # Keep status as 'pending' for retry or set to a specific agent_error status
                    reminder.status = "pending"  # Or "delivery_agent_error"
                    delivery_successful = False
                # Assuming agent sends "ack" on success
                elif "result" in jsonrpc_response and jsonrpc_response.get("result") == "ack":
                    current_app.logger.info(
                        f"JSON-RPC delivery successful for reminder ID {reminder.id} to agent '{agent_name}'. Received ACK.")
                    delivery_successful = True
                else:
                    current_app.logger.warning(
                        f"JSON-RPC delivery for reminder ID {reminder.id} to agent '{agent_name}' resulted in an unexpected response: {jsonrpc_response}")
                    reminder.status = "pending"  # Or "delivery_agent_response_invalid"
                    delivery_successful = False
            else:
                # send_jsonrpc_message returned None (timeout or send error before agent response)
                current_app.logger.error(
                    f"JSON-RPC send/ACK failed for reminder ID {reminder.id} to agent '{agent_name}'. No response or timeout.")
                reminder.status = "failed_delivery"  # Explicit send failure
                delivery_successful = False
        else:
            # Fallback to STDOUT
            delivery_method_used = "STDOUT"
            current_app.logger.info(
                f"Reminder ID: {reminder.id} has no process_name. Delivering via STDOUT.")
            project_name = "N/A"
            task_title = "N/A"

            task_title = "N/A"
            project_name = "N/A"

            delivery_message = f"""
--------------------------------------------------
REMINDER DELIVERY (FALLBACK):
ID: {reminder.id}
Message: {reminder.message}
Due: {reminder.next_run.isoformat()}
Project: {project_name}
Task: {task_title}
Delivery Method: STDOUT
--------------------------------------------------
"""
            print(delivery_message)
            current_app.logger.info(
                f"Reminder ID: {reminder.id} delivered successfully via STDOUT.")
            delivery_successful = True

        # --- Status and Recurrence Update Logic (conditional on successful delivery) ---
        if delivery_successful:
            try:
                interval_unit: Optional[str] = None
                interval_value: Optional[int] = None

                if reminder.recurrence:
                    recurrence_parts = reminder.recurrence.lower().split(':')
                    if len(recurrence_parts) == 1:
                        simple_unit = recurrence_parts[0]
                        if simple_unit == "daily":
                            interval_unit = "days"
                            interval_value = 1
                        elif simple_unit == "weekly":
                            interval_unit = "weeks"
                            interval_value = 1
                        else:
                            current_app.logger.warning(
                                f"Unsupported simple recurrence format: {reminder.recurrence} for reminder ID: {reminder.id}")
                    elif len(recurrence_parts) == 2:
                        interval_unit = recurrence_parts[0]
                        try:
                            interval_value = int(recurrence_parts[1])
                            if interval_value <= 0:
                                current_app.logger.warning(
                                    f"Invalid recurrence value: {recurrence_parts[1]} for reminder ID: {reminder.id}")
                                interval_unit = None
                                interval_value = None
                        except ValueError:
                            current_app.logger.warning(
                                f"Non-integer recurrence value: {recurrence_parts[1]} for reminder ID: {reminder.id}")
                            interval_unit = None
                            interval_value = None
                    else:
                        current_app.logger.warning(
                            f"Invalid recurrence format: {reminder.recurrence} for reminder ID: {reminder.id}")

                if interval_unit and interval_value is not None:
                    current_next_run_for_calc = reminder.next_run
                    new_next_run = calculate_next_run(
                        current_next_run_for_calc,
                        interval_unit,
                        interval_value
                    )
                    if new_next_run:
                        reminder.next_run = new_next_run
                        reminder.status = "pending"  # Rescheduled
                        current_app.logger.info(
                            f"Reminder ID: {reminder.id} (delivered via {delivery_method_used}) rescheduled to {new_next_run.isoformat()}. Status set to 'pending'.")
                    else:
                        # Failed to calculate next run, implies invalid recurrence logic for this path
                        reminder.status = "failed_reschedule"  # More specific status
                        current_app.logger.error(
                            f"Reminder ID: {reminder.id} (delivered via {delivery_method_used}) could not be rescheduled due to invalid recurrence parameters ('{interval_unit}', {interval_value}). Status set to 'failed_reschedule'.")
                else:
                    # Non-recurring or invalid recurrence, and delivery was successful
                    reminder.status = "sent"
                    current_app.logger.info(
                        f"Reminder ID: {reminder.id} (delivered via {delivery_method_used}) marked as 'sent' (was non-recurring or recurrence was invalid).")

                db.session.add(reminder)
                db.session.commit()
                current_app.logger.info(
                    f"Reminder ID: {reminder.id} status and/or next_run updated successfully in DB after {delivery_method_used} delivery.")

            except SQLAlchemyError as e_db:
                db.session.rollback()
                current_app.logger.error(
                    f"Database error updating reminder ID {reminder.id} after {delivery_method_used} delivery: {e_db}", exc_info=True)
            except Exception as e_update:
                current_app.logger.error(
                    f"Unexpected error updating status/recurrence for reminder ID {reminder.id} after {delivery_method_used} delivery: {e_update}", exc_info=True)
        else:  # Delivery failed
            # If delivery failed, we might have already set a specific status like 'failed_delivery' or 'pending' (if agent error)
            # We should ensure the status reflects this and commit if necessary.
            # If not already set by delivery logic
            if reminder.status not in ["failed_delivery", "pending", "delivery_agent_error", "delivery_agent_response_invalid"]:
                reminder.status = "failed_delivery"  # Generic failure if not more specific
            try:
                db.session.add(reminder)
                db.session.commit()
                current_app.logger.info(
                    f"Reminder ID: {reminder.id} status '{reminder.status}' persisted after failed delivery attempt via {delivery_method_used}.")
            except SQLAlchemyError as e_db_fail:
                db.session.rollback()
                current_app.logger.error(
                    f"Database error persisting status for reminder ID {reminder.id} after failed {delivery_method_used} delivery: {e_db_fail}", exc_info=True)

    except Exception as e:
        current_app.logger.error(
            f"Error during delivery process for reminder ID {reminder.id} (method: {delivery_method_used}): {e}", exc_info=True)
        # Ensure reminder status reflects a failure if an unexpected error occurs before status update logic
        # If it was pending and no specific failure status set
        if not delivery_successful and reminder.status == "pending":
            reminder.status = "failed_delivery_exception"
            try:
                db.session.add(reminder)
                db.session.commit()
            except Exception as e_final_commit:
                current_app.logger.error(
                    f"Failed to commit final failure status for reminder {reminder.id}: {e_final_commit}")


def monitor_agents_job(app: Flask):
    """Scheduled job to monitor and restart agents if necessary."""
    with app.app_context():
        app.logger.info(
            f"Job '{MONITOR_AGENTS_JOB_ID}': Starting agent monitoring...")
        try:
            process_manager = get_process_manager()
            all_agents = process_manager.process_registry.list_processes()
            if not all_agents:
                app.logger.info(
                    f"Job '{MONITOR_AGENTS_JOB_ID}': No agents registered to monitor.")
            else:
                app.logger.info(
                    f"Job '{MONITOR_AGENTS_JOB_ID}': Monitoring {len(all_agents)} agents.")
                for agent_details in all_agents:
                    agent_name = agent_details['name']
                    command = agent_details['command']
                    status = process_manager.check_agent_status(agent_name)
                    app.logger.debug(
                        f"Agent '{agent_name}' current status: {status}")

                    if status is None or status == "terminated" or status.startswith("failed_"):
                        app.logger.warning(
                            f"Agent '{agent_name}' found with status '{status}'. Attempting restart via job.")
                        restarted_info = process_manager.start_agent(
                            agent_name, command)
                        if restarted_info and restarted_info.get('status') == "running":
                            app.logger.info(
                                f"Agent '{agent_name}' restarted successfully by job with new PID {restarted_info.get('pid')}.")
                        else:
                            app.logger.error(
                                f"Job failed to restart agent '{agent_name}'. Current details: {restarted_info}")
            app.logger.info(
                f"Job '{MONITOR_AGENTS_JOB_ID}': Agent monitoring cycle complete.")

        except Exception as e:
            app.logger.error(
                f"Error in '{MONITOR_AGENTS_JOB_ID}': {e}", exc_info=True)
        app.logger.info(
            f"Job '{MONITOR_AGENTS_JOB_ID}': Finished.")


def check_overdue_reminders():
    """Check for overdue reminders and log them."""
    with current_app.app_context():
        current_app.logger.info("Job 'check_overdue_reminders': Starting...")
        try:
            now_utc = dt.datetime.now(dt.timezone.utc)
            overdue_reminders_query = Reminder.query.filter(
                # type: ignore
                Reminder.next_run < now_utc).filter(Reminder.status == "pending")
            overdue_reminders = cast(
                List[Reminder], overdue_reminders_query.all())

            if overdue_reminders:
                current_app.logger.info(
                    f"Found {len(overdue_reminders)} overdue reminders")
                for reminder in overdue_reminders:
                    try:
                        current_app.logger.info(
                            f"Overdue reminder ID: {reminder.id}, Task ID: {reminder.task_id}, Message: '{reminder.message[:50]}...'"
                        )
                    except Exception as e_log:
                        current_app.logger.error(
                            f"Error logging details for overdue reminder ID {reminder.id}: {e_log}", exc_info=True)
            else:
                current_app.logger.debug("No overdue reminders found")
        except SQLAlchemyError as e_db:
            current_app.logger.error(
                f"Database error in check_overdue_reminders: {e_db}", exc_info=True)
        except Exception as e_general:
            current_app.logger.error(
                f"Unexpected error in check_overdue_reminders: {e_general}", exc_info=True)
        current_app.logger.info("Job 'check_overdue_reminders': Finished.")


def send_reminder_notifications():
    """Send notifications for upcoming reminders (placeholder)."""
    with current_app.app_context():
        current_app.logger.info(
            "Job 'send_reminder_notifications': Starting...")
        try:
            now_utc = dt.datetime.now(dt.timezone.utc)
            one_hour_from_now = now_utc + dt.timedelta(hours=1)
            upcoming_reminders_query = Reminder.query.filter(Reminder.next_run <= one_hour_from_now).filter(  # type: ignore
                # type: ignore
                Reminder.next_run > now_utc).filter(Reminder.status == "pending")
            upcoming_reminders = cast(
                List[Reminder], upcoming_reminders_query.all())

            if upcoming_reminders:
                current_app.logger.info(
                    f"Found {len(upcoming_reminders)} upcoming reminders")
                for reminder in upcoming_reminders:
                    try:
                        current_app.logger.info(
                            f"Upcoming reminder ID: {reminder.id}, Task ID: {reminder.task_id}, Message: '{reminder.message[:50]}...'"
                        )
                        # TODO: Implement actual notification sending
                    except Exception as e_log:
                        current_app.logger.error(
                            f"Error logging details for upcoming reminder ID {reminder.id}: {e_log}", exc_info=True)
            else:
                current_app.logger.debug("No upcoming reminders found")
        except SQLAlchemyError as e_db:
            current_app.logger.error(
                f"Database error in send_reminder_notifications: {e_db}", exc_info=True)
        except Exception as e_general:
            current_app.logger.error(
                f"Unexpected error in send_reminder_notifications: {e_general}", exc_info=True)
        current_app.logger.info("Job 'send_reminder_notifications': Finished.")


def register_jobs(app: Flask):
    """Register scheduled jobs with Flask-APScheduler."""
    with app.app_context():
        app.logger.info("Attempting to register scheduled jobs...")

        # Register check_due_reminders job
        try:
            if scheduler.get_job(CHECK_DUE_REMINDERS_JOB_ID) is None:  # type: ignore
                scheduler.add_job(  # type: ignore
                    id=CHECK_DUE_REMINDERS_JOB_ID,
                    func='app.jobs.reminder_jobs:check_due_reminders',
                    args=[app],
                    trigger='interval',
                    seconds=60,  # Check every 60 seconds
                    replace_existing=True
                )
                app.logger.info(
                    f"Scheduled job '{CHECK_DUE_REMINDERS_JOB_ID}' to run every 60 seconds.")
            else:
                app.logger.info(
                    f"Job '{CHECK_DUE_REMINDERS_JOB_ID}' already scheduled.")
        except Exception as e_scheduler:
            app.logger.error(
                f"Error registering/checking job '{CHECK_DUE_REMINDERS_JOB_ID}': {e_scheduler}", exc_info=True)

        # Register monitor_agents_job
        try:
            if scheduler.get_job(MONITOR_AGENTS_JOB_ID) is None:  # type: ignore
                scheduler.add_job(  # type: ignore
                    id=MONITOR_AGENTS_JOB_ID,
                    func='app.jobs.reminder_jobs:monitor_agents_job',
                    args=[app],
                    trigger='interval',
                    seconds=45,  # Run every 45 seconds (adjust as needed)
                    replace_existing=True
                )
                app.logger.info(
                    f"Scheduled job '{MONITOR_AGENTS_JOB_ID}' to run every 45 seconds.")
            else:
                app.logger.info(
                    f"Job '{MONITOR_AGENTS_JOB_ID}' already scheduled.")
        except Exception as e_scheduler:
            app.logger.error(
                f"Error registering/checking job '{MONITOR_AGENTS_JOB_ID}': {e_scheduler}", exc_info=True)

        # Register check_overdue_reminders job
        try:
            if scheduler.get_job('check_overdue_reminders') is None:  # type: ignore
                scheduler.add_job(  # type: ignore
                    id='check_overdue_reminders',
                    func='app.jobs.reminder_jobs:check_overdue_reminders',
                    trigger='interval',
                    minutes=30,
                    replace_existing=True
                )
                app.logger.info(
                    "Scheduled job 'check_overdue_reminders' to run every 30 minutes.")
            else:
                app.logger.info(
                    "Job 'check_overdue_reminders' already scheduled.")
        except Exception as e_scheduler:
            app.logger.error(
                f"Error registering/checking job 'check_overdue_reminders': {e_scheduler}", exc_info=True)

        try:
            if scheduler.get_job('send_reminder_notifications') is None:  # type: ignore
                scheduler.add_job(  # type: ignore
                    id='send_reminder_notifications',
                    func='app.jobs.reminder_jobs:send_reminder_notifications',
                    trigger='interval',
                    minutes=15,
                    replace_existing=True
                )
                app.logger.info(
                    "Scheduled job 'send_reminder_notifications' to run every 15 minutes.")
            else:
                app.logger.info(
                    "Job 'send_reminder_notifications' already scheduled.")
        except Exception as e_scheduler:
            app.logger.error(
                f"Error registering/checking job 'send_reminder_notifications': {e_scheduler}", exc_info=True)

        app.logger.info("Scheduled jobs registration process completed.")
